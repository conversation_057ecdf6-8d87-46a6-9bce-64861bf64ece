# BIMEX Developer Plugin - Quick Install Script
Write-Host "========================================" -ForegroundColor Green
Write-Host "BIMEX Developer Plugin - Quick Install" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Define Revit 2024 addins path
$revitPath = "$env:APPDATA\Autodesk\Revit\Addins\2024"

# Create directory if it doesn't exist
if (!(Test-Path $revitPath)) {
    Write-Host "Creating Revit addins directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $revitPath -Force | Out-Null
}

# Check if compiled files exist
if (!(Test-Path "bin\Release\net48\BimexDeveloperPlugin.dll")) {
    Write-Host "ERROR: Plugin not compiled. Run build first." -ForegroundColor Red
    exit 1
}

Write-Host "Installing plugin files..." -ForegroundColor Yellow

try {
    # Copy DLL
    Copy-Item "bin\Release\net48\BimexDeveloperPlugin.dll" $revitPath -Force
    Write-Host "✓ BimexDeveloperPlugin.dll copied" -ForegroundColor Green
    
    # Copy .addin file
    Copy-Item "BimexDeveloperPlugin.addin" $revitPath -Force
    Write-Host "✓ BimexDeveloperPlugin.addin copied" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Installation completed successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Plugin installed to: $revitPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "NEW FEATURES IN THIS VERSION:" -ForegroundColor Yellow
    Write-Host "• Step 6.5: Visual confirmation of 3D view activation" -ForegroundColor White
    Write-Host "• Enhanced view switching with multiple fallback methods" -ForegroundColor White
    Write-Host "• Better user feedback during 3D view activation" -ForegroundColor White
    Write-Host "• Improved visual confirmation before OBJ export" -ForegroundColor White
    Write-Host ""
    Write-Host "WORKFLOW UPDATED:" -ForegroundColor Yellow
    Write-Host "1. Create new model" -ForegroundColor White
    Write-Host "2. Create 5x5 floor" -ForegroundColor White
    Write-Host "3. Select family file" -ForegroundColor White
    Write-Host "4. Load and position family" -ForegroundColor White
    Write-Host "5. Remove temporary floor" -ForegroundColor White
    Write-Host "6. Create and configure 3D view" -ForegroundColor White
    Write-Host "6.5. ★ ACTIVATE 3D VIEW FOR VISUAL CONFIRMATION ★" -ForegroundColor Magenta
    Write-Host "7. Export to OBJ format" -ForegroundColor White
    Write-Host "8. Save Revit model" -ForegroundColor White
    Write-Host ""
    Write-Host "Restart Revit to see the updated 'BIMEX Developer' tab" -ForegroundColor Cyan
    
} catch {
    Write-Host "ERROR: Failed to copy files - $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
