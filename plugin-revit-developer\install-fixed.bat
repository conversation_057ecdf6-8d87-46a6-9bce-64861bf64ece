@echo off
echo ========================================
echo BIMEX Developer Plugin v11.0 - CORREÇÃO DE TRANSAÇÃO
echo ========================================

REM Definir pasta de destino do Revit 2024
set "REVIT_ADDINS_PATH=%APPDATA%\Autodesk\Revit\Addins\2024"

echo ATENÇÃO: Feche o Revit antes de continuar!
echo.
echo Esta versão corrige o erro:
echo "Modification of the document is forbidden"
echo.
pause

REM Verificar se a pasta existe, se não, criar
if not exist "%REVIT_ADDINS_PATH%" (
    echo Criando pasta de Add-ins do Revit...
    mkdir "%REVIT_ADDINS_PATH%"
)

REM Verificar se os arquivos compilados existem
if not exist "bin\Release\net48\BimexDeveloperPlugin.dll" (
    echo ERRO: Plugin não compilado. Execute build.bat primeiro.
    pause
    exit /b 1
)

echo Tentando parar processos do Revit...
taskkill /f /im Revit.exe >nul 2>&1

echo Aguardando 3 segundos...
timeout /t 3 >nul

echo Copiando arquivos corrigidos para a pasta de Add-ins do Revit...

REM Copiar DLL corrigida
copy /Y "bin\Release\net48\BimexDeveloperPlugin.dll" "%REVIT_ADDINS_PATH%\"
if %errorlevel% neq 0 (
    echo ERRO: Falha ao copiar BimexDeveloperPlugin.dll
    pause
    exit /b 1
)

REM Copiar arquivo .addin
copy /Y "BimexDeveloperPlugin.addin" "%REVIT_ADDINS_PATH%\"
if %errorlevel% neq 0 (
    echo ERRO: Falha ao copiar BimexDeveloperPlugin.addin
    pause
    exit /b 1
)

echo.
echo ========================================
echo INSTALAÇÃO v11.0 CONCLUÍDA COM SUCESSO!
echo ========================================
echo.
echo CORREÇÕES IMPLEMENTADAS:
echo ✓ Erro de transação resolvido
echo ✓ Regeneração segura implementada
echo ✓ Vista 3D com confirmação visual
echo ✓ Múltiplos fallbacks para robustez
echo.
echo NOVO FLUXO (8.5 PASSOS):
echo 1. Criar novo modelo
echo 2. Criar piso 5x5 metros
echo 3. Selecionar família (.rfa)
echo 4. Carregar e posicionar família
echo 5. Remover piso temporário
echo 6. Criar e configurar vista 3D
echo 6.5. ★ ATIVAR VISTA 3D PARA CONFIRMAÇÃO VISUAL ★
echo 7. Exportar para formato OBJ
echo 8. Salvar modelo Revit
echo.
echo O plugin foi instalado em:
echo %REVIT_ADDINS_PATH%
echo.
echo Agora você pode abrir o Revit e testar o plugin corrigido.
echo O erro de transação não deve mais aparecer!
echo.
pause
